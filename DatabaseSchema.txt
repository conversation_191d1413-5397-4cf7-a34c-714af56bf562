-- Table: teams
CREATE TABLE teams (
    internal_id UUID PRIMARY KEY,
    id TEXT UNIQUE NOT NULL,
    created_at TIMESTAMPTZ NOT NULL,
    next_charge_at TIMESTAMPTZ,
    balance NUMERIC NOT NULL
    owner_internal_id UUID,
    owner_id TEXT,
    FOREIGN KEY (owner_id) REFERENCES teams(id)
);

-- Table: devices
CREATE TABLE devices (
    internal_id UUID PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL,
    team_internal_id UUID NOT NULL,
    team_id TEXT NOT NULL,
    ip TEXT,
    role TEXT,
    nickname TEXT,
    last_auth_at TIMESTAMPTZ,
    vpn_conf TEXT,
    msg_conf TEXT,
    phone_conf TEXT,
    fcm_token TEXT,
    FOREIGN KEY (team_internal_id) REFERENCES teams(internal_id)
    FOREIGN KEY (team_id) REFERENCES teams(id)
);

-- Table: transactions
CREATE TABLE transactions (
    internal_id INT8 PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL,
    team_internal_id UUID NOT NULL,
    team_id TEXT NOT NULL,
    amount NUMERIC NOT NULL,
    description TEXT,
    balance_before NUMERIC NOT NULL,
    balance_after NUMERIC NOT NULL,
    FOREIGN KEY (team_internal_id) REFERENCES teams(internal_id)
);

-- Table: deposits
CREATE TABLE deposits (
    internal_id INT8 PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL,
    team_internal_id UUID NOT NULL,
    team_id TEXT NOT NULL,
    amount NUMERIC NOT NULL,
    wallet TEXT NOT NULL,
    currency TEXT NOT NULL,
    expires_at TIMESTAMPTZ NOT NULL,
    rate NUMERIC NOT NULL,
    FOREIGN KEY (team_internal_id) REFERENCES teams(internal_id)
);