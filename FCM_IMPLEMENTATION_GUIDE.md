# FCM Token Implementation Guide

## Overview

This document describes the new database-based FCM (Firebase Cloud Messaging) token management system that replaces the previous file-based approach.

## What Changed

### Before (File-based)
- FCM tokens stored in `../tokens.txt`
- Only supported single token (most recent)
- No association with teams or devices
- Manual file management

### After (Database-based)
- FCM tokens stored in `devices.fcm_token` column
- Supports multiple tokens per team
- Device-specific token management
- Automatic token cleanup for invalid tokens

## Database Schema

The `devices` table includes:
```sql
CREATE TABLE devices (
    internal_id UUID PRIMARY KEY,
    team_id TEXT NOT NULL,
    fcm_token TEXT,  -- New field for FCM tokens
    -- ... other fields
);
```

## API Changes

### 1. Token Registration Endpoint: `/api/notifications/token`

**Request Format (from Mobile App):**
```json
{
  "token": "fcm-token-string",
  "customer_id": "*************"
}
```

Where:
- `token`: FCM registration token from the mobile app
- `customer_id`: IP address of the device (corresponds to `devices.ip` field)

**Response:**
```json
{
  "success": true,
  "message": "FCM token updated successfully",
  "deviceId": "device-uuid",
  "teamId": "team-123"
}
```

### 2. Notification Sending Endpoint: `/api/notifications`

**New Request Format:**
```json
{
  "title": "Notification Title",
  "message": "Notification message",
  "teamId": "team-123",           // Optional: send to specific team
  "deviceId": "device-identifier", // Optional: send to specific device
  "data": {                       // Optional: additional data
    "key": "value"
  }
}
```

## New Functions

### `updateFcmToken(teamId, fcmToken, deviceId?)`
- Updates FCM token for a device
- If `deviceId` provided, updates specific device
- Otherwise updates most recent device in team

### `getFcmTokens(teamId, deviceId?)`
- Retrieves FCM tokens for a team
- Optionally filters by device ID
- Returns array of valid tokens

### `sendNotification(options)`
- Enhanced notification sending
- Supports team-based and device-specific notifications
- Automatic invalid token cleanup
- Batch sending with error handling

## Usage Examples

### Android App Token Registration
```javascript
// Send FCM token to server (matches mobile app format)
fetch('/api/notifications/token', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'pass': 'your-auth-token'
  },
  body: JSON.stringify({
    token: fcmToken,
    customer_id: deviceIpAddress // IP address of the device
  })
});
```

**Mobile App Format (Kotlin/Java):**
```kotlin
val requestBody = "{" +
    "\"token\":\"$token\"," +
    "\"customer_id\":\"$customerId\"" +
"}"
```

### Sending Notifications
```javascript
// Send to specific team
await sendNotification({
  title: 'Team Update',
  message: 'New feature available',
  teamId: 'team-123'
});

// Send to specific device
await sendNotification({
  title: 'Device Alert',
  message: 'Maintenance required',
  teamId: 'team-123',
  deviceId: 'device-456'
});

// Backward compatibility (uses most recent token)
await sendNotification({
  title: 'General Alert',
  message: 'System maintenance'
});
```

## Admin Interface

The admin notification page now includes:
- Team ID field (optional)
- Device ID field (optional)
- Improved error handling and feedback

## Migration Notes

### Automatic Migration
- Existing `tokens.txt` file is no longer used
- New system is backward compatible
- Old API calls still work (uses most recent token)

### Recommended Steps
1. Update Android app to send `team_id` with tokens
2. Test notification sending with new parameters
3. Monitor logs for any token-related errors
4. Remove old `tokens.txt` file when confident

## Error Handling

### Invalid Tokens
- Automatically detected during sending
- Invalid tokens removed from database
- Detailed error reporting in API responses

### Missing Tokens
- Clear error messages when no tokens found
- Graceful fallback to most recent token
- Admin interface shows token status

## Testing

Run the test script to verify implementation:
```bash
node test-fcm-implementation.js
```

## Security Considerations

- FCM tokens are sensitive data
- Database access properly secured
- Token cleanup prevents accumulation of invalid tokens
- Authentication required for all endpoints

## Monitoring

Check logs for:
- Token registration success/failures
- Notification delivery status
- Invalid token cleanup
- Database operation errors
