# FCM Token Implementation Guide

## Overview

This document describes the new database-based FCM (Firebase Cloud Messaging) token management system that replaces the previous file-based approach.

## What Changed

### Before (File-based)
- FCM tokens stored in `../tokens.txt`
- Only supported single token (most recent)
- No association with teams or devices
- Manual file management

### After (Database-based)
- FCM tokens stored in `devices.fcm_token` column
- Supports multiple tokens per team
- Device-specific token management
- Automatic token cleanup for invalid tokens

## Database Schema

The `devices` table includes:
```sql
CREATE TABLE devices (
    internal_id UUID PRIMARY KEY,
    team_id TEXT NOT NULL,
    fcm_token TEXT,  -- New field for FCM tokens
    -- ... other fields
);
```

## API Changes

### 1. Token Registration Endpoint: `/api/notifications/token`

**Request Format (from Mobile App):**
```json
{
  "token": "fcm-token-string",
  "customer_id": "*************"
}
```

Where:
- `token`: FCM registration token from the mobile app
- `customer_id`: IP address of the device (corresponds to `devices.ip` field)

**Response:**
```json
{
  "success": true,
  "message": "FCM token updated successfully",
  "deviceId": "device-uuid",
  "teamId": "team-123"
}
```

### 2. Notification Sending Endpoint: `/api/notifications`

**Request Format:**
```json
{
  "title": "Notification Title",
  "message": "Notification message",
  "teamId": "team-123",           // Required: team to send to
  "deviceId": "device-identifier", // Optional: send to specific device
  "data": {                       // Optional: additional data
    "key": "value"
  }
}
```

## New Functions

### `updateFcmTokenByIp(ipAddress, fcmToken)`
- Updates FCM token for a device by IP address
- Finds device using `devices.ip = ipAddress`
- Returns device and team information

### `getFcmTokens(teamId, deviceId?)`
- Retrieves FCM tokens for a team
- Optionally filters by device ID
- Returns array of valid tokens

### `sendNotification(options)`
- Team-based notification sending (teamId required)
- Supports device-specific notifications
- Automatic invalid token cleanup
- Batch sending with error handling

## Usage Examples

### Android App Token Registration
```javascript
// Send FCM token to server (matches mobile app format)
fetch('/api/notifications/token', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'pass': 'your-auth-token'
  },
  body: JSON.stringify({
    token: fcmToken,
    customer_id: deviceIpAddress // IP address of the device
  })
});
```

**Mobile App Format (Kotlin/Java):**
```kotlin
val requestBody = "{" +
    "\"token\":\"$token\"," +
    "\"customer_id\":\"$customerId\"" +
"}"
```

### Sending Notifications
```javascript
// Send to all devices in a team
await sendNotification({
  title: 'Team Update',
  message: 'New feature available',
  teamId: 'team-123'
});

// Send to specific device in a team
await sendNotification({
  title: 'Device Alert',
  message: 'Maintenance required',
  teamId: 'team-123',
  deviceId: '*************' // IP address
});
```

## Admin Interface

The admin notification page (`/admin/notifications`) now features an improved UX:

### **Device Selection Dropdown**
- **Specific Devices**: Choose individual devices with FCM tokens
- **Team Broadcasting**: Send to all devices in a team
- **Custom Team ID**: Manual team ID entry for flexibility

### **Smart Features**
- **Automatic Team Assignment**: When selecting a device, team ID is assigned automatically
- **FCM Token Status**: Visual indicators (✅/❌) show which devices have FCM tokens
- **Device Summary**: Shows total devices, FCM-enabled devices, and team count
- **Real-time Feedback**: Detailed success/error messages with delivery statistics

### **Interface Elements**
- Device dropdown with organized optgroups
- Dynamic form fields (custom team ID appears when needed)
- Visual feedback for notification delivery status
- Responsive design with clear visual hierarchy

## Migration Notes

### Changes from File-based System
- No longer uses `tokens.txt` file
- All tokens now stored in database
- Team ID is required for all notifications

### Migration Steps
1. Ensure all devices have proper IP addresses in database
2. Update any existing notification code to include teamId
3. Test notification sending with new API format
4. Remove old `tokens.txt` file
5. Access improved admin interface at `/admin/notifications`

## Admin Interface Usage

### **Sending to Specific Device**
1. Select device from "Specific Devices" section
2. Team ID is automatically assigned
3. Notification sent only to that device

### **Sending to All Devices in Team**
1. Select team from "All Devices" section
2. Notification sent to all devices with FCM tokens in that team

### **Custom Team ID**
1. Select "Custom Team ID" option
2. Enter team ID manually
3. Useful for teams not yet in the device list

## Error Handling

### Invalid Tokens
- Automatically detected during sending
- Invalid tokens removed from database
- Detailed error reporting in API responses

### Missing Tokens
- Clear error messages when no tokens found
- Graceful fallback to most recent token
- Admin interface shows token status

## Testing

Run the test script to verify implementation:
```bash
node test-fcm-implementation.js
```

## Security Considerations

- FCM tokens are sensitive data
- Database access properly secured
- Token cleanup prevents accumulation of invalid tokens
- Authentication required for all endpoints

## Monitoring

Check logs for:
- Token registration success/failures
- Notification delivery status
- Invalid token cleanup
- Database operation errors
