<script>
    import { enhance } from '$app/forms';

    export let data;

    let loading = false;
    let error = null;
    let success = false;
    let title = '';
    let message = '';
    let selectedDevice = '';
    let customTeamId = '';
    let showCustomTeam = false;
    let lastResult = null;

    // Reset error/success on input
    function onInput() {
        error = null;
        success = false;
        lastResult = null;
    }

    function handleSubmit() {
        loading = true;
        error = null;
        success = false;
    }

    function handleResult(result) {
        loading = false;
        result = result?.data;
        if (result?.success) {
            success = true;
            lastResult = result;
            title = '';
            message = '';
            selectedDevice = '';
            customTeamId = '';
            showCustomTeam = false;
        } else if (result?.error) {
            error = result.error;
        } else if (result?.type === 'error' && result?.status) {
            error = `Server error (${result.status})`;
        } else {
            error = 'Unknown error occurred.';
        }
    }
</script>

<div class="container">
    <h1>Notification Testing</h1>

    {#if data.error}
        <div class="error">
            <strong>Error loading devices:</strong> {data.error}
        </div>
    {/if}

    <div class="form-section">
        <h2>Send Test Notification</h2>
        <form 
            method="POST" 
            action="?/sendNotification"
            use:enhance={(form) => {
                handleSubmit();
                return async ({ result }) => handleResult(result);
            }}
        >
            <div class="form-group">
                <label for="title">Title:</label>
                <input 
                    type="text" 
                    id="title" 
                    name="title"
                    bind:value={title} 
                    required
                    placeholder="Notification title"
                    on:input={onInput}
                />
            </div>

            <div class="form-group">
                <label for="message">Message:</label>
                <textarea
                    id="message"
                    name="message"
                    bind:value={message}
                    required
                    placeholder="Notification message"
                    on:input={onInput}
                ></textarea>
            </div>

            <div class="form-group">
                <label for="selectedDevice">Send to device:</label>
                <select
                    id="selectedDevice"
                    name="selectedDevice"
                    bind:value={selectedDevice}
                    required
                    on:change={() => {
                        showCustomTeam = selectedDevice === 'custom';
                        onInput();
                    }}
                >
                    <option value="">Select a device...</option>
                    {#each data.devices as device}
                        <option value={device.internal_id}>
                            📱 {device.display_name} {device.has_fcm_token ? '✅' : '❌'}
                        </option>
                    {/each}
                    <option value="custom">🔧 Custom Team ID (all devices)</option>
                </select>
                <small>
                    {#if selectedDevice && selectedDevice !== 'custom'}
                        Will send to the specific device (team assigned automatically)
                    {:else if selectedDevice === 'custom'}
                        Enter a custom team ID to send to all devices in that team
                    {:else}
                        Choose a device to send notification to
                    {/if}
                </small>
            </div>

            {#if showCustomTeam}
                <div class="form-group">
                    <label for="customTeamId">Team ID (for broadcast):</label>
                    <input
                        type="text"
                        id="customTeamId"
                        name="customTeamId"
                        bind:value={customTeamId}
                        required
                        placeholder="Enter team ID"
                        on:input={onInput}
                    />
                    <small>Send to all devices with FCM tokens in this team</small>
                </div>
            {/if}

            <button type="submit" disabled={loading}>
                {loading ? 'Sending...' : 'Send Notification'}
            </button>
        </form>

        {#if error}
            <div class="error">{error}</div>
        {/if}

        {#if success}
            <div class="success">
                <strong>Notification sent successfully!</strong>
                {#if lastResult?.sentTo}
                    <br><small>Sent to: {lastResult.sentTo}</small>
                {/if}
                {#if lastResult?.response}
                    <br><small>
                        Delivered to {lastResult.response.successful || 0} device(s)
                        {#if lastResult.response.failed > 0}
                            ({lastResult.response.failed} failed)
                        {/if}
                    </small>
                {/if}
            </div>
        {/if}
    </div>
</div>

<style>
    .container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
    }

    .form-section {
        margin-bottom: 2rem;
    }

    .info-section {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 4px;
        margin-bottom: 2rem;
        border-left: 4px solid #007bff;
    }

    .info-section h3 {
        margin: 0 0 0.5rem 0;
        color: #333;
    }

    .info-section p {
        margin: 0;
        color: #666;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: bold;
    }

    input, textarea, select {
        width: 100%;
        padding: 0.5rem;
        margin-bottom: 0.5rem;
        border: 1px solid #ccc;
        border-radius: 4px;
        font-size: 14px;
    }

    select {
        background-color: white;
        cursor: pointer;
    }

    optgroup {
        font-weight: bold;
        color: #333;
    }

    option {
        font-weight: normal;
        padding: 0.25rem;
    }

    small {
        display: block;
        color: #666;
        font-size: 0.85em;
        margin-bottom: 1rem;
        margin-top: -0.5rem;
    }

    textarea {
        min-height: 100px;
    }

    button {
        background-color: #4CAF50;
        color: white;
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }

    button:disabled {
        background-color: #cccccc;
        cursor: not-allowed;
    }

    .error {
        color: #ff0000;
        margin-top: 1rem;
    }

    .success {
        color: #4CAF50;
        margin-top: 1rem;
    }

    .notifications-section {
        margin-top: 2rem;
    }

    .notifications-list {
        margin-top: 1rem;
    }

    .notification {
        background-color: #f5f5f5;
        padding: 1rem;
        margin-bottom: 1rem;
        border-radius: 4px;
    }

    .notification h3 {
        margin: 0 0 0.5rem 0;
    }

    .notification p {
        margin: 0 0 0.5rem 0;
    }

    .notification small {
        color: #666;
    }
</style> 