<script>
    import { enhance } from '$app/forms';

    export let data;

    let loading = false;
    let error = null;
    let success = false;
    let title = '';
    let message = '';
    let selectedDevice = '';
    let lastResult = null;

    // Reset error/success on input
    function onInput() {
        error = null;
        success = false;
        lastResult = null;
    }

    function handleSubmit() {
        loading = true;
        error = null;
        success = false;
    }

    function handleResult(result) {
        loading = false;
        result = result?.data;
        if (result?.success) {
            success = true;
            lastResult = result;
            title = '';
            message = '';
            selectedDevice = '';
        } else if (result?.error) {
            error = result.error;
        } else if (result?.type === 'error' && result?.status) {
            error = `Server error (${result.status})`;
        } else {
            error = 'Unknown error occurred.';
        }
    }
</script>

<div class="admin-card">
    <div class="admin-card-header">
        <div class="admin-card-title">FCM Notifications</div>
    </div>



    {#if data.error}
        <div class="message error">
            <strong>Error loading devices:</strong> {data.error}
        </div>
    {/if}

    <form
        method="POST"
        action="?/sendNotification"
        use:enhance={() => {
            handleSubmit();
            return async ({ result }) => handleResult(result);
        }}
    >
        <div class="form-group">
            <label for="title" class="form-label">Title:</label>
            <input
                type="text"
                id="title"
                name="title"
                bind:value={title}
                required
                placeholder="Notification title"
                on:input={onInput}
                class="form-input"
            />
        </div>

        <div class="form-group">
            <label for="message" class="form-label">Message:</label>
            <textarea
                id="message"
                name="message"
                bind:value={message}
                required
                placeholder="Notification message"
                on:input={onInput}
                class="form-input"
            ></textarea>
        </div>

        <div class="form-group">
            <label for="selectedDevice" class="form-label">Send to device:</label>
            <select
                id="selectedDevice"
                name="selectedDevice"
                bind:value={selectedDevice}
                required
                on:change={onInput}
                class="form-input"
            >
                <option value="">Select a device...</option>
                {#each data.devices as device}
                    <option value={device.internal_id}>
                        📱 {device.display_name} {device.has_fcm_token ? '✅' : '❌'}
                    </option>
                {/each}
            </select>
            <small>
                {#if selectedDevice}
                    Will send to the specific device (team assigned automatically)
                {:else}
                    Choose a device to send notification to
                {/if}
            </small>
        </div>



        <button type="submit" disabled={loading}>
            {loading ? 'Sending...' : 'Send Notification'}
        </button>
    </form>

    {#if error}
        <div class="message error">{error}</div>
    {/if}

    {#if success}
        <div class="message success">
            <strong>Notification sent successfully!</strong>
            {#if lastResult?.sentTo}
                <br><small>Sent to: {lastResult.sentTo}</small>
            {/if}
            {#if lastResult?.response}
                <br><small>
                    Delivered to {lastResult.response.successful || 0} device(s)
                    {#if lastResult.response.failed > 0}
                        ({lastResult.response.failed} failed)
                    {/if}
                </small>
            {/if}
        </div>
    {/if}
</div>

<style>
    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: white;
    }

    .form-input {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #ddd;
        border-radius: 6px;
        font-size: 14px;
        transition: border-color 0.2s;
    }

    .form-input:focus {
        outline: none;
        border-color: #5b6eff;
        box-shadow: 0 0 0 3px rgba(91, 110, 255, 0.1);
    }

    select.form-input {
        background-color: #2a2a2a;
        color: white;
        cursor: pointer;
    }

    select.form-input option {
        background-color: #2a2a2a;
        color: white;
    }

    textarea.form-input {
        min-height: 100px;
        resize: vertical;
    }

    small {
        display: block;
        color: #666;
        font-size: 0.85em;
        margin-top: 0.25rem;
    }

    button {
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.2s;
        background-color: #5b6eff;
        color: white;
    }

    button:hover:not(:disabled) {
        background-color: #4c5fd7;
        transform: translateY(-1px);
    }

    button:disabled {
        background-color: #6c757d;
        cursor: not-allowed;
        transform: none;
    }

    .message {
        padding: 1rem;
        border-radius: 6px;
        margin-bottom: 1rem;
    }

    .message.error {
        background-color: #fef2f2;
        color: #dc2626;
        border: 1px solid #fecaca;
    }

    .message.success {
        background-color: #f0fdf4;
        color: #16a34a;
        border: 1px solid #bbf7d0;
    }
</style>