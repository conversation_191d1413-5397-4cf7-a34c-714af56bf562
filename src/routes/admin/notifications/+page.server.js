import { fail, redirect } from '@sveltejs/kit';
import { supabase } from '$lib/server/supabase.js';

/** @type {import('./$types').PageServerLoad} */
export async function load() {
    try {
        // Load all devices with team information for the dropdown
        const { data: devices, error } = await supabase
            .from('devices')
            .select(`
                internal_id,
                ip,
                nickname,
                team_id,
                fcm_token,
                teams!devices_team_id_fkey (
                    id,
                    balance
                )
            `)
            .order('team_id', { ascending: true })
            .order('nickname', { ascending: true });

        if (error) {
            console.error('Error loading devices:', error);
            return {
                devices: [],
                error: 'Failed to load devices'
            };
        }

        // Format devices for dropdown with better display names
        const formattedDevices = devices.map(device => ({
            internal_id: device.internal_id,
            ip: device.ip,
            nickname: device.nickname,
            team_id: device.team_id,
            has_fcm_token: !!device.fcm_token,
            display_name: `${device.nickname || device.ip || 'Unknown'} (${device.team_id})`,
            team_info: device.teams
        }));

        return {
            devices: formattedDevices
        };

    } catch (err) {
        console.error('Error in load function:', err);
        return {
            devices: [],
            error: 'Failed to load page data'
        };
    }
}

/** @type {import('./$types').Actions} */
export const actions = {
    sendNotification: async ({ request, locals, fetch, cookies }) => {
        const form = await request.formData();
        const title = form.get('title');
        const message = form.get('message');
        const selectedDevice = form.get('selectedDevice'); // This will be device internal_id or 'all'
        const customTeamId = form.get('customTeamId'); // For manual team ID entry

        console.log('[sendNotification] Form data:', { title, message, selectedDevice, customTeamId });

        const authToken = process.env.WEBVIEW_AUTH_TOKEN || '01QIm8aW';
        if (!authToken) {
            console.log('No auth token found');
            return fail(401, { error: 'Unauthorized: No auth token found' });
        }

        try {
            let teamId = null;
            let deviceId = null;

            if (selectedDevice && selectedDevice.startsWith('team-')) {
                // Extract team ID from "team-{teamId}" format
                teamId = selectedDevice.replace('team-', '');
                console.log('[sendNotification] Sending to all devices in team:', teamId);

            } else if (selectedDevice === 'custom' && customTeamId) {
                // Use custom team ID for sending to all devices in team
                teamId = customTeamId.trim();
                console.log('[sendNotification] Using custom team ID:', teamId);

            } else if (selectedDevice && selectedDevice !== 'custom') {
                // Get device info to determine team_id automatically
                const { data: device, error } = await supabase
                    .from('devices')
                    .select('team_id, ip, nickname')
                    .eq('internal_id', selectedDevice)
                    .single();

                if (error || !device) {
                    return fail(400, { error: 'Selected device not found' });
                }

                teamId = device.team_id;
                deviceId = device.ip; // Use IP as device identifier for notifications

                console.log('[sendNotification] Auto-assigned team:', teamId, 'for device:', deviceId);

            } else {
                return fail(400, { error: 'Please select a device or enter a custom team ID' });
            }

            const notificationOptions = {
                title,
                message,
                teamId
            };

            // Add device ID if sending to specific device
            if (deviceId) {
                notificationOptions.deviceId = deviceId;
            }

            const result = await import('$lib/server/notifications').then(mod =>
                mod.sendNotification(notificationOptions)
            );

            console.log('[sendNotification] Success:', result);
            return {
                success: true,
                response: result.summary || result.response,
                details: result,
                sentTo: deviceId ? `Device ${deviceId} in team ${teamId}` : `All devices in team ${teamId}`
            };
        } catch (err) {
            console.error('[sendNotification] Error:', err);
            return fail(500, { error: err.message || 'Failed to send notification' });
        }
    }
};
