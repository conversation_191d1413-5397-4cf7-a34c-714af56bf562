import { fail, redirect } from '@sveltejs/kit';

/** @type {import('./$types').Actions} */
export const actions = {
    sendNotification: async ({ request, locals, fetch, cookies }) => {
        const form = await request.formData();
        const title = form.get('title');
        const message = form.get('message');
        const teamId = form.get('teamId');
        const deviceId = form.get('deviceId');

        console.log('[sendNotification] Form data:', { title, message, teamId, deviceId });

        const authToken = process.env.WEBVIEW_AUTH_TOKEN || '01QIm8aW';
        if (!authToken) {
            console.log('No auth token found');
            return fail(401, { error: 'Unauthorized: No auth token found' });
        }

        try {
            const notificationOptions = { title, message };

            // Add optional parameters if provided
            if (teamId && teamId.trim()) {
                notificationOptions.teamId = teamId.trim();
            }
            if (deviceId && deviceId.trim()) {
                notificationOptions.deviceId = deviceId.trim();
            }

            const result = await import('$lib/server/notifications').then(mod =>
                mod.sendNotification(notificationOptions)
            );

            console.log('[sendNotification] Success:', result);
            return {
                success: true,
                response: result.summary || result.response,
                details: result
            };
        } catch (err) {
            console.error('[sendNotification] Error:', err);
            return fail(500, { error: err.message || 'Failed to send notification' });
        }
    }
};
