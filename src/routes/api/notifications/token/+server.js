// SvelteKit POST endpoint to receive FCM tokens from Android app
import { json } from '@sveltejs/kit';
import { updateFcmToken } from '$lib/server/notifications.js';

const WEBVIEW_AUTH_TOKEN = process.env.WEBVIEW_AUTH_TOKEN || '01QIm8aW';

/** @type {import('./$types').RequestHandler} */
export async function POST({ request }) {


    const pass = request.headers.get('pass');

    if (pass !== WEBVIEW_AUTH_TOKEN) {
        console.warn('[POST /api/notifications/token] Unauthorized access attempt');
        return new Response('Unauthorized', { status: 401 });
    }


    let token, teamId, deviceId;
    try {
        const body = await request.json();
        token = body.token;
        teamId = body.team_id || body.customer_id; // Support both field names for backward compatibility
        deviceId = body.device_id || body.device_identifier; // Optional device identifier

        console.log('[POST /api/notifications/token] Received data from Android app:', {
            token: token ? token.substring(0, 20) + '...' : null,
            teamId,
            deviceId
        });

        if (!token) {
            console.error('[POST /api/notifications/token] Missing token in request body');
            return json({ error: 'Missing token' }, { status: 400 });
        }

        if (!teamId) {
            console.error('[POST /api/notifications/token] Missing team_id in request body');
            return json({ error: 'Missing team_id' }, { status: 400 });
        }
    } catch (e) {
        console.error('[POST /api/notifications/token] Invalid request body:', e);
        return json({ error: 'Invalid request body' }, { status: 400 });
    }

    // Store token in database
    try {
        const result = await updateFcmToken(teamId, token, deviceId);
        console.log('[POST /api/notifications/token] Successfully updated FCM token:', result);

        return json({
            success: true,
            message: 'FCM token updated successfully',
            deviceId: result.deviceId,
            updateType: result.updated
        });

    } catch (err) {
        console.error('[POST /api/notifications/token] Failed to update FCM token:', err);
        return json({
            error: 'Failed to update FCM token',
            details: err.message
        }, { status: 500 });
    }
}
