// @ts-check

import { json } from '@sveltejs/kit';
import { sendNotification, getRegistrationToken } from '$lib/server/notifications';

/** @type {import('./$types').RequestHandler} */
export async function GET({ request }) {
  const authToken = request.headers.get('X-Phantom-Auth');
  if (!authToken) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }
  // FCM is push-based; no notifications to "get" unless you store them elsewhere
  return json({ notifications: [] });
}

/** @type {import('./$types').RequestHandler} */
export async function POST({ request }) {
  const authToken = request.headers.get('X-Phantom-Auth');
  if (!authToken) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const body = await request.json();
    const { title, message, teamId, deviceId, data } = body;

    // Validate required fields
    if (!teamId) {
      return json({ error: 'Team ID is required' }, { status: 400 });
    }

    console.log('[POST /api/notifications] Sending notification:', {
      title,
      message: message ? message.substring(0, 50) + '...' : null,
      teamId,
      deviceId,
      hasData: !!data
    });

    try {
      const result = await sendNotification({
        title,
        message,
        teamId,
        deviceId,
        data
      });

      return json({
        success: true,
        ...result
      });

    } catch (err) {
      console.error('[POST /api/notifications] Notification error:', err);

      if (err.message === 'Missing required fields') {
        return json({ error: err.message }, { status: 400 });
      }
      if (err.message.includes('No') && err.message.includes('token')) {
        return json({ error: err.message }, { status: 404 });
      }
      return json({ error: err.message }, { status: 500 });
    }
  } catch (error) {
    console.error('[POST /api/notifications] Request parsing error:', error);
    return json({ error: 'Invalid request body' }, { status: 400 });
  }
}